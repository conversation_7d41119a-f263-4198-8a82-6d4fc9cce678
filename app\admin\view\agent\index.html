{layout name="layout1" /}
<style>
    .layui-form-label {
        width: 120px;
    }

    /* 优化操作按钮样式 */
    .operate-btn-group {
        display: flex;
        flex-wrap: wrap;
        gap: 5px;
        /* 增加按钮间距 */
        align-items: center;
        justify-content: flex-start;
    }

    .operate-btn {
        padding: 5px 10px !important;
        /* 增加内边距 */
        font-size: 13px !important;
        /* 增大字体 */
        line-height: 1.4 !important;
        /* 调整行高 */
        border-radius: 4px !important;
        /* 轻微增加圆角 */
        min-width: auto !important;
        height: auto !important;
        margin: 2px !important;
        /* 调整外边距 */
        transition: all 0.2s ease;
    }

    .operate-btn:hover {
        opacity: 0.8;
        transform: translateY(-1px);
    }

    /* 按钮颜色主题 */
    .btn-primary {
        background: #1E9FFF !important;
        border-color: #1E9FFF !important;
    }

    .btn-success {
        background: #5FB878 !important;
        border-color: #5FB878 !important;
    }

    .warning {
        background: #FFB800 !important;
        border-color: #FFB800 !important;
    }

    .btn-danger {
        background: #FF5722 !important;
        border-color: #FF5722 !important;
    }

    .btn-info {
        background: #01AAED !important;
        border-color: #01AAED !important;
    }

    .btn-dark {
        background: #393D49 !important;
        border-color: #393D49 !important;
    }
</style>
<div class="wrapper">
    <div class="layui-card">
        <!--操作提示-->
        <div class="layui-card-body">
            <div class="layui-collapse like-layui-collapse" lay-accordion="" style="border:1px dashed #c4c4c4">
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title like-layui-colla-title" style="background-color: #fff">操作提示</h2>
                    <div class="layui-colla-content layui-show">
                        <p>*招商顾问保证金总额；<b style="color: red;font-size:20px;">{$total}</b></p>
                    </div>
                </div>
            </div>
        </div>
        <!--搜索区域-->
        <div class="layui-card-body layui-form">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <div class="layui-form-label">用户信息：</div>
                    <div class="layui-input-inline">
                        <input type="text" id="keyword" name="keyword" class="layui-input" />
                    </div>
                </div>
                <!--                <div class="layui-inline">-->
                <!--                    <div class="layui-form-label">代理等级：</div>-->
                <!--                    <div class="layui-input-inline">-->
                <!--                        <select name="level_id" id="level_id"  placeholder="请选择" >-->
                <!--                            <option value="all">全部</option>-->

                <!--                            <option value="1">一级代理</option>-->
                <!--                            <option value="2">二级代理</option>-->

                <!--                        </select>-->
                <!--                    </div>-->
                <!--                </div>-->
                <div class="layui-inline">
                    <div class="layui-form-label">代理状态：</div>
                    <div class="layui-input-inline">
                        <select name="is_freeze" id="is_freeze" placeholder="请选择">
                            <option value="all">全部</option>
                            <option value="0">正常</option>
                            <option value="1">冻结</option>
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <div class="layui-form-label">删除状态：</div>
                    <div class="layui-input-inline">
                        <select name="del" id="del" placeholder="请选择">
                            <option value="all">全部</option>
                            <option value="0" selected>正常</option>
                            <option value="1">已删除</option>
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <button class="layui-btn layui-btn-primary layui-bg-blue" lay-submit lay-filter="search">搜索</button>
                    <button class="layui-btn layui-btn-primary" lay-submit lay-filter="reset">重置</button>
                </div>
            </div>
        </div>
        <div class="layui-card-body">
            <!--功能按钮-->
            <div class="btns">
                <button class="layui-btn layui-btn-sm layui-bg-blue" id="open">开通代理会员</button>
                <!-- <button class="layui-btn layui-btn-sm layui-bg-blue" id="deposit">代理保证金管理</button> -->
            </div>
            <!--数据表格-->
            <table id="lists" lay-filter="lists"></table>
            <!--工具条模板-->
            <script type="text/html" id="operate">
                <div class="operate-btn-group">
                    <a class="layui-btn layui-btn-xs operate-btn btn-primary" lay-event="detail">代理设置</a>
                    {{#  if(d.is_freeze == 0){ }}
                    <!-- <a class="layui-btn layui-btn-xs operate-btn btn-danger" lay-event="freeze">冻结资格</a> -->
                    {{#  } else { }}
                    <a class="layui-btn layui-btn-xs operate-btn btn-success" lay-event="unfreeze">恢复资格</a>
                    {{#  } }}
                    {{#  if(d.deposit_status == 7 || d.deposit_status == '7'){ }}
                    <a class="layui-btn layui-btn-xs operate-btn btn-info" lay-event="audit">审核</a>
                    {{#  } else if(d.deposit_status == 2 || d.deposit_status == '2'){ }}
                    <a class="layui-btn layui-btn-xs operate-btn btn-warning" lay-event="refund">微信退款</a>
                    <a class="layui-btn layui-btn-xs operate-btn btn-danger" lay-event="manualRefund">手动退款</a>
                    {{#  } }}
                    {{#  if(d.deposit_id > 0){ }}
                    <a class="layui-btn layui-btn-xs operate-btn btn-danger" lay-event="depositDetails">保证金明细</a>
                    {{#  } }}
                </div>
            </script>
            <!--自定义模板-->
            <script type="text/html" id="user-info">
                <div style="display: flex; align-items: center; cursor: pointer;" lay-event="detail">
                    <img src="{{d.avatar}}" style="height:60px;width: 60px" class="image-show">
                    <div class="layui-input-inline"  style="text-align: left; margin-left: 10px;">
                        <p>用户编号:{{d.user_sn}}</p>
                        <p>用户昵称:{{d.nickname}}</p>
                    </div>
                </div>
            </script>
            <script type="text/html" id="level-info">
                {{d.level}}
            </script>
            <script type="text/html" id="earnings-wait">
                {{d.earnings.wait}}
            </script>
            <script type="text/html" id="earnings-success">
                {{d.earnings.success}}
            </script>
            <script type="text/html" id="earnings-fail">
                {{d.earnings.fail}}
            </script>
            <script type="text/html" id="user-distribution">
                {{#  if(d.is_freeze){ }}
                冻结
                {{#  } else { }}
                正常
                {{#  } }}
            </script>
            <script type="text/html" id="user-count">
                <a href="javascript:;" class="layui-table-link" lay-event="viewUsers">{{d.user_count || 0}}</a>
            </script>
            <script type="text/html" id="member-count">
                <a href="javascript:;" class="layui-table-link" lay-event="viewMembers">{{d.member_count || 0}}</a>
            </script>
            <script type="text/html" id="merchant-count">
                <a href="javascript:;" class="layui-table-link" lay-event="viewMerchants">{{d.merchant_count || 0}}</a>
            </script>
            <script type="text/html" id="agent-count">
                <a href="javascript:;" class="layui-table-link" lay-event="viewAgents">{{d.agent_count || 0}}</a>
            </script>
            <script type="text/html" id="deposit-status">
                {{#  if(d.deposit_status_text == '未支付'){ }}
                <span class="layui-badge layui-bg-gray">未支付</span>
                {{#  } else if(d.deposit_status_text == '已支付'){ }}
                <span class="layui-badge layui-bg-blue">已支付</span>
                {{#  } else if(d.deposit_status_text == '公示期结束(可退)'){ }}
                <span class="layui-badge layui-bg-green">公示期结束(可退)</span>
                {{#  } else if(d.deposit_status_text == '退款申请中(公示期)'){ }}
                <span class="layui-badge layui-bg-orange">退款申请中(公示期)</span>
                {{#  } else if(d.deposit_status_text == '已退款'){ }}
                <span class="layui-badge layui-bg-cyan">已退款</span>
                {{#  } else if(d.deposit_status_text == '退款失败'){ }}
                <span class="layui-badge layui-bg-red">退款失败</span>
                {{#  } else if(d.deposit_status_text == '补缴'){ }}
                <span class="layui-badge layui-bg-orange">补缴</span>
                {{#  } else if(d.deposit_status_text == '待审核'){ }}
                <span class="layui-badge layui-bg-yellow">待审核</span>
                {{#  } else if(d.deposit_status_text == '退款申请中'){ }}
                <span class="layui-badge layui-bg-orange">退款申请中</span>
                {{#  } else if(d.deposit_status_text == '审核拒绝'){ }}
                <span class="layui-badge layui-bg-red">审核拒绝</span>
                {{#  } else { }}
                <span class="layui-badge layui-bg-gray">{{d.deposit_status_text || '未知状态'}}</span>
                {{#  } }}
            </script>
            <script type="text/html" id="is-admin-created">
                {{#  if(d.is_admin_created == 1){ }}
                <span class="layui-badge layui-bg-red">后台开通</span>
                {{#  } else { }}
                <span class="layui-badge layui-bg-blue">线上开通</span>
                {{#  } }}
            </script>
        </div>
    </div>
</div>


<script>

    layui.config({
        version: "{$front_version}",
        base: '/static/lib/'
    }).use(['table', 'form'], function () {
        let $ = layui.$
            , form = layui.form
            , table = layui.table;

        //监听搜索
        form.on('submit(search)', function (data) {
            var field = data.field;
            //执行重载
            table.reload('lists', {
                where: field,
                page: { curr: 1 }
            });
        });

        //清空查询
        form.on('submit(reset)', function () {
            $('#keyword').val('');
            $('#is_freeze').val('all');
            $('#del').val('0'); // 默认显示未删除的代理
            form.render('select');
            //刷新列表
            table.reload('lists', {
                where: { del: 0 }, // 默认只显示未删除的代理
                page: { curr: 1 }
            });
        });

        // 数据表格渲染
        table.render({
            elem: '#lists'
            , url: '{:url("agent.agent/index")}' //数据接口
            , method: 'post'
            , where: { del: 0 } // 默认只显示未删除的代理
            , page: true //开启分页
            , cols: [[ //表头
                { templet: '#user-info', title: '用户信息', width: 320 }
                //,{templet: '#level-info', title: '代理等级', width:180}
                // ,{templet: '#earnings-success', title: '已入账佣金', width:120}
                // ,{templet: '#earnings-wait', title: '待结算佣金', width:120}
                // ,{templet: '#earnings-fail', title: '已罚扣佣金', width:120}
                , { templet: '#user-distribution', title: '代理状态', width: 60 }
                , { templet: '#user-count', title: '我的用户', width: 100, align: 'center' }
                , { templet: '#member-count', title: '我的会员', width: 100, align: 'center' }
                , { templet: '#merchant-count', title: '我的商家', width: 100, align: 'center' }
                , { templet: '#agent-count', title: '我的代理', width: 100, align: 'center' }
                , { field: 'deposit_amount', title: '保证金金额', width: 100, align: 'center' }
                , { field: 'deposit_current_balance', title: '当前余额', width: 100, align: 'center' }
                , { field: 'deposit_amount2', title: '后台调整金额', width: 120, align: 'center' }
                , { templet: '#is-admin-created', title: '开通方式', width: 100, align: 'center' }
                , { templet: '#deposit-status', title: '保证金状态', width: 100, align: 'center' }
                //,{field: 'distribution_start_time', title: '代理开始时间', width: 200}
                //,{field: 'distribution_end_time', title: '代理失效时间', width: 200}
                , { title: '操作', toolbar: '#operate', width: 240 }
            ]]
            , text: { none: '暂无数据！' }
            , parseData: function (res) { //将原始数据解析成 table 组件所规定的数据
                return {
                    "code": res.code,
                    "msg": res.msg,
                    "count": res.data.count, //解析数据长度
                    "data": res.data.lists, //解析数据列表
                };
            },
            response: {
                statusCode: 1
            }
            , done: function (res, curr, count) {
                // 解决操作栏因为内容过多换行问题
                $(".layui-table-main tr").each(function (index, val) {
                    $($(".layui-table-fixed-l .layui-table-body tbody tr")[index]).height($(val).height());
                    $($(".layui-table-fixed-r .layui-table-body tbody tr")[index]).height($(val).height());
                });
            }
        });

        // 工具条事件
        table.on('tool(lists)', function (obj) {
            var layEvent = obj.event; //获得 lay-event 对应的值（也可以是表头的 event 参数对应的值）

            if (layEvent === 'freeze') { // 冻结资格
                layer.confirm('确定要冻结资格吗?将同步冻结未结算佣金', function (index) {
                    layer.close(index);
                    like.ajax({
                        url: "{:url('agent.agent/isFreeze')}",
                        data: { user_id: obj.data.user_id, is_freeze: 1 },
                        type: "post",
                        success: function (res) {
                            if (res.code === 1) {
                                layui.layer.msg(res.msg);
                                layer.close(index);
                                table.reload("lists");
                            }
                        }
                    });
                });
            } else if (layEvent === 'unfreeze') { // 恢复资格
                layer.confirm('确定要恢复资格吗?将同步解冻未结算佣金', function (index) {
                    layer.close(index);
                    like.ajax({
                        url: "{:url('agent.agent/isFreeze')}",
                        data: { user_id: obj.data.user_id, is_freeze: 0 },
                        type: "post",
                        success: function (res) {
                            if (res.code === 1) {
                                layui.layer.msg(res.msg);
                                layer.close(index);
                                table.reload("lists");
                            }
                        }
                    });
                });
            } else if (layEvent === 'detail') { // 代理设置
                id = obj.data.user_id;
                layer.open({
                    type: 2
                    , title: "代理详情"
                    , content: "{:url('agent.agent/detail')}?id=" + id
                    , area: ["60%", "80%"]
                    , maxmin: true
                });
            } else if (layEvent === 'viewUsers' || layEvent === 'viewMembers' || layEvent === 'viewMerchants' || layEvent === 'viewAgents') {
                // 查看用户列表
                var type = 0; // 默认为我的用户
                var title = "我的用户";

                if (layEvent === 'viewMembers') {
                    type = 1;
                    title = "我的会员";
                } else if (layEvent === 'viewMerchants') {
                    type = 2;
                    title = "我的商家";
                } else if (layEvent === 'viewAgents') {
                    type = 3;
                    title = "我的代理";
                }

                // 打开弹窗
                layer.open({
                    type: 1
                    , title: title + "详情"
                    , area: ['800px', '600px']
                    , content: '<div class="layui-card"><div class="layui-card-body"><table id="user-detail-table" lay-filter="user-detail-table"></table></div></div>'
                    , success: function (layero, index) {
                        // 渲染表格
                        table.render({
                            elem: '#user-detail-table'
                            , url: '{:url("agent.agent/getUsersByType")}' // 后端接口
                            , where: {
                                user_id: obj.data.user_id,
                                type: type
                            }
                            , parseData: function (res) { // 解析数据格式
                                console.log('接收到的用户数据:', res); // 调试输出
                                return {
                                    "code": res.code === 1 ? 0 : res.code, // 将成功状态码1转换为0
                                    "msg": res.msg,
                                    "count": res.data.count,
                                    "data": res.data.lists
                                };
                            }
                            , response: {
                                statusCode: 0 // 规定成功的状态码，默认：0
                            }
                            , cols: [[
                                {
                                    field: 'avatar', title: '头像', width: 80, templet: function (d) {
                                        return '<img src="' + d.avatar + '" style="height:50px;width:50px;" class="image-show">';
                                    }
                                }
                                , { field: 'nickname', title: '用户昵称', width: 120 }
                                , { field: 'mobile', title: '手机号码', width: 120 }
                                , { field: 'service_info', title: '购买的服务', width: 150 }
                                , { field: 'service_fee', title: '结算佣金', width: 100 }
                                , { field: 'user_delete', title: '状态', width: 100 }
                                , { field: 'create_time', title: '创建时间', width: 160 }
                            ]]
                            , page: true
                            , limit: 10
                            , text: { none: '暂无数据！' }
                            , done: function (res) { // 表格渲染完成回调
                                console.log('表格渲染完成:', res); // 调试输出
                            }
                        });
                    }
                });
            } else if (layEvent === 'refund') { // 微信退款
                layer.confirm('确定要进行微信退款吗？', function (index) {
                    layer.close(index);
                    like.ajax({
                        url: "{:url('agent.agent/refundDeposit')}",
                        data: { id: obj.data.deposit_id },
                        type: "post",
                        success: function (res) {
                            if (res.code === 1) {
                                layui.layer.msg(res.msg);
                                table.reload("lists");
                            } else {
                                layui.layer.msg(res.msg);
                            }
                        }
                    });
                });
            } else if (layEvent === 'manualRefund') { // 手动退款
                // 打开手动退款页面
                layer.open({
                    type: 2,
                    title: '手动退款',
                    area: ['600px', '500px'],
                    content: '{:url("agent.agent/manualRefund")}?id=' + obj.data.deposit_id,
                    maxmin: true
                });
            } else if (layEvent === 'audit') { // 审核
                // 打开审核弹窗
                layer.open({
                    type: 2,
                    title: '保证金退款审核',
                    area: ['800px', '600px'],
                    content: '{:url("agent.agent/auditRefund")}?id=' + obj.data.deposit_id,
                    maxmin: true,
                    btn: ['通过', '拒绝', '取消'],
                    yes: function (index, layero) {
                        // 审核通过
                        var iframeWindow = window['layui-layer-iframe' + index];
                        var opinion = layero.find('iframe').contents().find('#audit_opinion').val();

                        like.ajax({
                            url: "{:url('agent.agent/processAudit')}",
                            data: {
                                id: obj.data.deposit_id,
                                action: 'approve',
                                opinion: opinion
                            },
                            type: "post",
                            success: function (res) {
                                if (res.code === 1) {
                                    layui.layer.msg(res.msg);
                                    layer.close(index);
                                    table.reload("lists");
                                } else {
                                    layui.layer.msg(res.msg);
                                }
                            }
                        });
                    },
                    btn2: function (index, layero) {
                        // 审核拒绝
                        var iframeWindow = window['layui-layer-iframe' + index];
                        var opinion = layero.find('iframe').contents().find('#audit_opinion').val();

                        if (!opinion || opinion.trim() === '') {
                            layui.layer.msg('拒绝时必须填写审核意见');
                            return false;
                        }

                        like.ajax({
                            url: "{:url('agent.agent/processAudit')}",
                            data: {
                                id: obj.data.deposit_id,
                                action: 'reject',
                                opinion: opinion
                            },
                            type: "post",
                            success: function (res) {
                                if (res.code === 1) {
                                    layui.layer.msg(res.msg);
                                    layer.close(index);
                                    table.reload("lists");
                                } else {
                                    layui.layer.msg(res.msg);
                                }
                            }
                        });
                        return false;
                    },
                    btn3: function (index) {
                        layer.close(index);
                    }
                });
            } else if (layEvent === 'depositDetails') { // 保证金明细
                // 打开保证金明细页面
                layer.open({
                    type: 2,
                    title: '保证金明细',
                    area: ['80%', '90%'],
                    content: '{:url("agent.agent/depositDetails")}?user_id=' + obj.data.user_id,
                    maxmin: true
                });
            }
        });

        //开通代理
        $('#open').click(function () {
            layer.open({
                type: 2,
                title: "开通代理会员",
                content: '{:url("agent.agent/openAgentForm")}',
                area: ['60%', '60%'],
                btn: ['确定', '取消'],
                yes: function (index, layero) {
                    var iframeWindow = window['layui-layer-iframe' + index];
                    var submit = layero.find('iframe').contents().find('#form-submit');
                    iframeWindow.layui.form.on('submit(form-submit)', function (data) {
                        like.ajax({
                            url: '{:url("agent.agent/open")}',
                            data: data.field,
                            type: 'post',
                            success: function (res) {
                                if (res.code === 1) {
                                    layui.layer.msg(res.msg, { icon: 1 });
                                    layer.close(index);
                                    table.reload('lists');
                                } else {
                                    layui.layer.msg(res.msg, { icon: 2 });
                                }
                            }
                        });
                        return false;
                    });
                    submit.trigger('click');
                }
            });
        });

        // 代理保证金管理
        $('#deposit').click(function () {
            layer.open({
                type: 2,
                title: '代理保证金管理',
                content: '{:url("agent.agent/deposit")}',
                area: ['90%', '90%'],
                maxmin: true
            });
        });
    });
</script>