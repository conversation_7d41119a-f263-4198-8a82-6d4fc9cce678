{layout name="layout2" /}

<link rel="stylesheet" href="/static/admin/css/goods.css" media="all">
<link href="__PUBLIC__/static/lib/layui/layeditor/layedit.css" rel="stylesheet" />
<script src="__PUBLIC__/static/lib/layui/layeditor/index.js"></script>
<script src="__PUBLIC__/static/lib/layui/layeditor/ace/ace.js"></script>

<div class="layui-tab layui-tab-card">
    <!--顶部切换页-->
    <ul class="layui-tab-title">
        <li class="goods-tab layui-this" style="color: #6a6f6c">分类与状态编辑</li>
        <li class="goods-tab" style="color: #6a6f6c">商品详情编辑</li>
    </ul>

    <!--切换内容-->
    <div class="layui-tab-content layui-form">
        <!--分类与状态编辑-->
        <div class="layui-tab-item layui-show goods-content">
            <!--平台分类-->
            <div class="layui-form-item">
                <label class="layui-form-label">平台分类：</label>
                <div class="layui-input-inline">
                    <select name="first_cate_id" lay-filter="first_category">
                        <option value="">请选择分类</option>
                    </select>
                </div>
                <div class="layui-input-inline">
                    <select name="second_cate_id" lay-filter="second_category">
                        <option value="">请选择分类</option>
                    </select>
                </div>
                <div class="layui-input-inline">
                    <select name="third_cate_id">
                        <option value="">请选择分类</option>
                    </select>
                </div>
            </div>

            <!--销售状态-->
            <div class="layui-form-item">
                <label class="layui-form-label">销售状态：</label>
                <div class="layui-input-block">
                    <input type="radio" name="status" value="1" title="上架">
                    <input type="radio" name="status" value="2" title="下架">
                </div>
            </div>

            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button class="layui-btn" lay-submit lay-filter="category-status-submit">保存分类与状态</button>
                </div>
            </div>
        </div>

        <!--商品详情编辑-->
        <div class="layui-tab-item goods-content">
            <div class="layui-form-item">
                <label class="layui-form-label">商品详情：</label>
                <div class="layui-input-block">
                    <textarea id="content" name="content" style="display: none;"></textarea>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button class="layui-btn" lay-submit lay-filter="content-submit">保存详情</button>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="__PUBLIC__/static/common/js/array.js"></script>

<script>
    var goods_id = '{$goods_id}';
    var categorys = { $category_lists | raw};
    var goods_info = { $info | raw};

    layui.config({
        version: "{$front_version}",
        base: '/static/lib/'
    }).extend({
        likeedit: 'likeedit/likeedit'
    }).use(['table', 'form', 'element', 'likeedit'], function () {
        var form = layui.form
            , $ = layui.$
            , element = layui.element
            , layEditor = layui.layEditor;

        //---------------------------------------平台分类联动 begin ----------------------------------
        setSelectFirst();

        function setSelectFirst(default_id) {
            var category_select_html = '<option value="">请选择分类</option>';
            for (var i in categorys) {
                if (categorys[i]['pid'] == 0) {
                    category_select_html += '<option value="' + categorys[i]['id'] + '">' + categorys[i]['name'] + '</option>';
                }
            }
            $('select[name="first_cate_id"]').html(category_select_html);
            if (default_id) {
                $('select[name="first_cate_id"]').val(default_id);
            }
            form.render('select');
        }

        function setSelectSecond(default_id, pid) {
            pid = pid === undefined ? $('select[name="first_cate_id"]').val() : pid;
            $('select[name="second_cate_id"]').html('<option value="">请选择分类</option>');
            $('select[name="third_cate_id"]').html('<option value="">请选择分类</option>');
            var category_select_html = '<option value="">请选择分类</option>';
            for (var i in categorys) {
                if (categorys[i]['pid'] == pid) {
                    category_select_html += '<option value="' + categorys[i]['id'] + '">' + categorys[i]['name'] + '</option>';
                }
            }
            $('select[name="second_cate_id"]').html(category_select_html);
            if (default_id) {
                $('select[name="second_cate_id"]').val(default_id);
            }
            form.render('select');
        }

        function setSelectThird(default_id, pid) {
            pid = pid === undefined ? $('select[name="second_cate_id"]').val() : pid;
            $('select[name="third_cate_id"]').html('<option value="">请选择分类</option>');
            var category_select_html = '<option value="">请选择分类</option>';
            for (var i in categorys) {
                if (categorys[i]['pid'] == pid) {
                    category_select_html += '<option value="' + categorys[i]['id'] + '">' + categorys[i]['name'] + '</option>';
                }
            }
            $('select[name="third_cate_id"]').html(category_select_html);
            if (default_id) {
                $('select[name="third_cate_id"]').val(default_id);
            }
            form.render('select');
        }

        // 监听一级分类选择
        form.on('select(first_category)', function (data) {
            setSelectSecond('', data.value);
        });
        // 监听二级分类选择
        form.on('select(second_category)', function (data) {
            setSelectThird('', data.value);
        });
        //---------------------------------------平台分类联动 end ----------------------------------

        //------------------------------------------富文本编辑器 begin --------------------------------
        layEditor.set({
            uploadImage: {
                url: '{:url("file/lists")}?type=10'
            },
        })
        var ieditor = layEditor.build('content')
        //------------------------------------------富文本编辑器 end --------------------------------

        // 初始化数据
        if (goods_info && goods_info.base) {
            // 设置分类
            setSelectFirst(goods_info.base.first_cate_id);
            setSelectSecond(goods_info.base.second_cate_id, goods_info.base.first_cate_id);
            setSelectThird(goods_info.base.third_cate_id, goods_info.base.second_cate_id);

            // 设置详情
            layEditor.setContent(ieditor, goods_info.base.content);

            // 设置状态
            $("input[name=status][value=" + goods_info.base.status + "]").prop("checked", true);
            form.render();
        }

        // 监听分类与状态提交
        form.on('submit(category-status-submit)', function (data) {
            data.field.goods_id = goods_id;

            // 先保存分类
            like.ajax({
                url: '{:url("editCategory")}',
                data: {
                    goods_id: goods_id,
                    first_cate_id: data.field.first_cate_id,
                    second_cate_id: data.field.second_cate_id,
                    third_cate_id: data.field.third_cate_id
                },
                success: function (res) {
                    if (res.code == 1) {
                        // 分类保存成功后，保存状态
                        like.ajax({
                            url: '{:url("editStatus")}',
                            data: {
                                goods_id: goods_id,
                                status: data.field.status
                            },
                            success: function (res2) {
                                layer.msg(res2.msg);
                                if (res2.code == 1) {
                                    parent.layer.closeAll();
                                    parent.location.reload();
                                }
                            }
                        });
                    } else {
                        layer.msg(res.msg);
                    }
                }
            });
            return false;
        });

        // 监听详情提交
        form.on('submit(content-submit)', function (data) {
            data.field.goods_id = goods_id;
            data.field.content = layEditor.getContent(ieditor);
            like.ajax({
                url: '{:url("editContent")}',
                data: data.field,
                success: function (res) {
                    layer.msg(res.msg);
                    if (res.code == 1) {
                        parent.layer.closeAll();
                        parent.location.reload();
                    }
                }
            });
            return false;
        });


    });
</script>