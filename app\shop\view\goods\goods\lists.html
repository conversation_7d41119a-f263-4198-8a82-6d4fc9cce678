{layout name="layout1" /}
<style>
    .layui-table-cell {
        height: auto;
    }

    #refresh-btn.disabled {
        background-color: #e6e6e6 !important;
        color: #999 !important;
        cursor: not-allowed;
    }
</style>
<div class="wrapper">
    <div class="layui-card">
        <!--操作提示-->
        <div class="layui-card-body">
            <div class="layui-collapse like-layui-collapse" lay-accordion="" style="border:1px dashed #c4c4c4">
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title like-layui-colla-title" style="background-color: #fff">操作提示</h2>
                    <div class="layui-colla-content layui-show">
                        <p>*商家发布商品需要平台审核，审核通过后，可以调整上下架状态。</p>
                        <p>*编辑未审核通过商品，可重新提交审核。</p>
                        <p>*审核通过后，重新编辑商品无需再次审核。</p>
                    </div>
                </div>
            </div>
        </div>

        <!--搜索条件-->
        <div class="layui-card-body layui-form">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">商品名称：</label>
                    <div class="layui-input-inline">
                        <input type="text" name="goods_name" id="goods_name" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">商品类型:</label>
                    <div class="layui-input-block">
                        <select name="goods_type" id="goods_type">
                            <option value="">全部</option>
                            {foreach $goods_type as $key => $val }
                            <option value="{$key}">{$val}</option>
                            {/foreach}
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">平台分类:</label>
                    <div class="layui-input-block">
                        <select name="platform_cate_id" id="platform_cate_id" placeholder="请选择平台商品分类">
                            <option value="0">全部</option>
                            {foreach $cate_list as $val }
                            <option value="{$val.id}">{$val.html}{$val.name}</option>
                            {/foreach}
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">店铺分类:</label>
                    <div class="layui-input-block">
                        <select name="shop_cate_id" id="shop_cate_id" placeholder="请选择商家商品分类">
                            <option value="0">全部</option>
                            {foreach $shop_cate_list as $val }
                            <option value="{$val.id}">{$val.name}</option>
                            {/foreach}
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <button class="layui-btn layui-btn-sm layui-btn-normal" lay-submit lay-filter="search">查询</button>
                    <button class="layui-btn layui-btn-sm layui-btn-primary" lay-submit
                        lay-filter="clear-search">重置</button>
                </div>
            </div>
        </div>

        <!-选项卡-->
            <div class="layui-tab layui-tab-card" lay-filter="like-tabs">
                <ul class="layui-tab-title">
                    <li data-type='1' class="layui-this">销售中商品({$statistics.sell})</li>
                    <li data-type='2'>库存预警商品({$statistics.warn})</li>
                    <li data-type='3'>仓库中商品({$statistics.warehouse})</li>
                    <li data-type='4'>回收站商品({$statistics.recycle})</li>
                    <li data-type='5'>待审核商品({$statistics.audit_stay})</li>
                    <li data-type='6'>审核未通过商品({$statistics.audit_refuse})</li>
                </ul>

                <div class="layui-tab-content" style="padding: 0 15px;">
                    <div style="margin-top: 10px" class="add">
                        <button class="layui-btn layui-btn-sm layEvent {$view_theme_color}"
                            lay-event="add">发布商品</button>
                        {if $tier_level > 0}
                        <button id="refresh-btn" class="layui-btn layui-btn-sm">刷新</button>
                        {/if}
                        <button id="upper" class="layui-btn layui-btn-sm layEvent layui-btn-primary" lay-event="upper"
                            style="display: none;">上架</button>
                        <button id="lower" class="layui-btn layui-btn-sm layEvent layui-btn-primary"
                            lay-event="lower">下架</button>
                    </div>

                    <table id="goods-lists" lay-filter="goods-lists"></table>

                    <script type="text/html" id="goods-info">
                    <img src="{{d.image}}" style="height:60px;width: 60px;margin-right: 5px;" class="image-show"> {{d.name}}
                </script>

                    <script type="text/html" id="price-info">
                   {{d.min_price}} ~ {{d.max_price}}
                </script>

                    <script type="text/html" id="ratio">
                    一级分销比例: {{d.first_ratio}}% <br />
                    二级分销比例: {{d.second_ratio}}% <br />
                    三级分销比例: {{d.third_ratio}}% <br />
                </script>

                    <script type="text/html" id="goods-operation">
                    {{# if( d.del != 2 ){ }}
                    <a class="layui-btn layui-btn-normal layui-btn-sm" lay-event="edit">编辑</a>
                    {{# } }}
                    {{# if( d.status == 1 && d.del == 0 && d.audit_status == 1 ){ }}
                    <a class="layui-btn layui-btn-danger layui-btn-sm" lay-event="status">下架</a>
                    {{# } }}
                    {{# if( d.status == 0 && d.del == 0 && d.audit_status == 1 ){ }}
                    <a class="layui-btn layui-btn-danger layui-btn-sm" lay-event="status">上架</a>
                    {{# } }}
                    {{# if( d.del != 2 && d.audit_status == 1 ){ }}
                    <a class="layui-btn layui-btn-danger layui-btn-sm" lay-event="recycle">移至回收站</a>
                    {{# } }}
                    {{# if( d.del == 2 && d.audit_status == 1 ){ }}
                    <a class="layui-btn layui-btn-danger layui-btn-sm" lay-event="back_to_warehouse">放回仓库</a>
                    {{# } }}
                    {{# if( (d.del == 2 && d.audit_status == 1) || ( d.del !=1 && d.audit_status == 0) || ( d.del != 1 && d.audit_status == 2) ){ }}
                    <a class="layui-btn layui-btn-danger layui-btn-sm" lay-event="del">删除</a>
                    {{# } }}
                </script>
                </div>
            </div>
    </div>
</div>

<script>
    layui.use(['table', 'form', 'element'], function () {
        var $ = layui.$
            , form = layui.form
            , table = layui.table
            , element = layui.element;

        // 页面加载时检查刷新按钮状态
        $(document).ready(function () {
            // 只有当刷新按钮存在时才执行检查
            if ($('#refresh-btn').length > 0) {
                checkRefreshButtonStatus();
            }
        });

        // 检查刷新按钮状态
        function checkRefreshButtonStatus() {
            var lastRefreshDate = localStorage.getItem('lastRefreshDate');
            var today = new Date().toISOString().split('T')[0];

            if (lastRefreshDate === today) {
                $('#refresh-btn').addClass('disabled').prop('disabled', true);
            }
        }

        // 刷新按钮点击事件
        $('#refresh-btn').on('click', function () {
            if ($(this).hasClass('disabled')) {
                return false;
            }

            // 禁用按钮并添加灰色样式
            $(this).addClass('disabled').prop('disabled', true);

            // 保存刷新日期到localStorage
            var today = new Date().toISOString().split('T')[0];
            localStorage.setItem('lastRefreshDate', today);

            // 生成50-200之间的随机数
            var increase = Math.floor(Math.random() * 151) + 50;

            // 弹窗提示
            layer.msg('刷新成功，预计曝光量将增加' + increase + ' ↑', {
                icon: 1,
                time: 3000
            });
        });

        //监听搜索
        form.on('submit(search)', function (data) {
            var field = data.field;
            //执行重载
            table.reload('goods-lists', {
                where: field,
                page: { curr: 1 }
            });
        });

        //清空查询
        form.on('submit(clear-search)', function () {
            $('#goods_name').val('');
            $('#platform_cate_id').val('');
            $('#shop_cate_id').val('');
            $('#goods_type').val('');
            form.render('select');
            //刷新列表
            table.reload('goods-lists', {
                where: [], page: { curr: 1 }
            });
        });

        //事件
        var active = {
            add: function () {
                layer.open({
                    type: 2
                    , title: '添加商品'
                    , content: '{:url("goods.goods/add")}'
                    , area: ['90%', '90%']
                    , btn: ['保存', '取消']
                    , maxmin: true
                    , yes: function (index, layero) {
                        var iframeWindow = window['layui-layer-iframe' + index]
                            , submitID = 'goods-submit'
                            , submit = layero.find('iframe').contents().find('#' + submitID);
                        //监听提交
                        iframeWindow.layui.form.on('submit(' + submitID + ')', function (data) {
                            var field = data.field;
                            like.ajax({
                                url: '{:url("goods.goods/add")}',
                                data: field,
                                type: "post",
                                success: function (res) {
                                    if (res.code == 1) {
                                        layui.layer.msg(res.msg, {
                                            offset: '15px'
                                            , icon: 1
                                            , time: 1000
                                        }, function () {
                                            window.location.href = window.location.href;
                                        });
                                    }
                                }
                            });
                        });
                        submit.trigger('click');
                    }
                    , cancel: function (index, layero) {
                        var window_index = index;
                        layer.confirm('商品未保存，确定关闭吗？', {
                            time: 0, //不自动关闭
                            btn: ['确定', '取消'],
                            yes: function (index) {
                                layer.close(index);
                                layer.close(window_index);
                            }
                        });
                        return false;
                    }
                });
            },
            //批量下架
            lower: function () {
                updateStatus(0);
            },
            //批量上架
            upper: function () {
                updateStatus(1);
            }
        };
        like.eventClick(active);


        //获取列表
        getList(1); // 初始加载获取销售中的商品
        //切换列表
        element.on('tab(like-tabs)', function (data) {
            var type = $(this).attr('data-type');
            // 重置搜索模块
            $('#goods_name').val('');
            $('#platform_cate_id').val('');
            $('#shop_cate_id').val('');
            form.render('select');
            // 重新获取商品列表
            getList(type);
            if (type === '1' || type === '2') {
                $("#lower").show();
                $("#upper").hide();
            } else if (type === '3') {
                $("#lower").hide();
                $("#upper").show();
            } else {
                $("#lower").hide();
                $("#upper").hide();
            }
        });

        //监听工具条
        table.on('tool(goods-lists)', function (obj) {
            var id = obj.data.id;
            var name = obj.data.name;
            //上下架
            if (obj.event === 'status') {
                var tips = '确定上架当前商品：' + '<span style="color: red">' + name + '</span>';

                var field_value = 1;

                if (obj.data.status) {
                    field_value = 0;
                    tips = '确定下架当前商品：' + '<span style="color: red">' + name + '</span>';
                }

                layer.confirm(tips, function (index) {
                    var fields = 'status';
                    changeFields(id, fields, field_value);
                })
            }
            //移至回收站
            if (obj.event === 'recycle') {
                var tips = '确定移至回收站：' + '<span style="color: red">' + name + '</span>';
                var field_value = 2;
                layer.confirm(tips, function (index) {
                    var fields = 'del';
                    changeFields(id, fields, field_value);
                })
            }

            if (obj.event === 'back_to_warehouse') {
                var tips = '确定放回仓库：' + '<span style="color: red">' + name + '</span>';
                layer.confirm(tips, function (index) {
                    like.ajax({
                        url: '{:url("goods.goods/backToWarehouse")}',
                        data: { id: id },
                        type: 'post',
                        dataType: 'json',
                        success: function (res) {
                            if (res.code == 1) {
                                layui.layer.msg(res.msg, {
                                    offset: '15px'
                                    , icon: 1
                                    , time: 1000
                                }, function () {
                                    // window.location.href = window.location.href;
                                    layer.close(index);
                                    updateTabNumber();
                                    table.reload('goods-lists');
                                });
                                // updateTabNumber();
                            }
                        }
                    })
                })
            }

            if (obj.event === 'edit') {
                var id = obj.data.id;
                layer.open({
                    type: 2
                    , title: '编辑商品'
                    , content: '{:url("goods.goods/edit")}?goods_id=' + id
                    , area: ['90%', '90%']
                    , btn: ['保存', '取消']
                    , maxmin: true
                    , yes: function (index, layero) {
                        var iframeWindow = window['layui-layer-iframe' + index]
                            , submitID = 'goods-submit'
                            , submit = layero.find('iframe').contents().find('#' + submitID);
                        //监听提交
                        iframeWindow.layui.form.on('submit(' + submitID + ')', function (data) {
                            var field = data.field;
                            like.ajax({
                                url: '{:url("goods.goods/edit")}',
                                data: field,
                                type: "post",
                                success: function (res) {
                                    if (res.code == 1) {
                                        layui.layer.msg(res.msg, {
                                            offset: '15px'
                                            , icon: 1
                                            , time: 1000
                                        }, function () {
                                            // window.location.href = window.location.href;
                                            layer.close(index);
                                            updateTabNumber();
                                            table.reload('goods-lists');
                                        });

                                    }
                                }
                            });
                        });
                        submit.trigger('click');
                    }
                    , cancel: function (index, layero) {
                        var window_index = index;
                        layer.confirm('商品未保存，确定关闭吗？', {
                            time: 0, //不自动关闭
                            btn: ['确定', '取消'],
                            yes: function (index) {
                                layer.close(index);
                                layer.close(window_index);
                            }
                        });
                        return false;
                    }
                });
            }

            if (obj.event === 'del') {
                var name = obj.data.name;
                layer.confirm('确定删除商品:' + '<span style="color: red">' + name + '</span>', function (index) {
                    like.ajax({
                        url: '{:url("goods.goods/del")}',
                        data: { id: id },
                        type: "post",
                        success: function (res) {
                            if (res.code == 1) {
                                layui.layer.msg(res.msg, {
                                    offset: '15px'
                                    , icon: 1
                                    , time: 1000
                                }, function () {
                                    // window.location.href = window.location.href;
                                    layer.close(index);
                                    updateTabNumber();
                                    table.reload('goods-lists');
                                });
                            }
                        }
                    });
                    layer.close(index);
                })
            }

            if (obj.event === 'tips') {
                layer.tips('数字越大，越靠前', $(this), { tips: [1, '#FF5722'], time: 1500 });
            }
        });

        //商品排序
        table.on('edit(goods-lists)', function (obj) {
            var id = obj.data.id;
            var fields = 'sort';
            var field_value = obj.value;
            if (isNaN(field_value)) {
                var old_value = $(this).prev().text();
                layer.tips('请输入数字', $(this), { tips: [1, '#FF5722'] });
                $(this).val(old_value);
                return false;
            }
            changeFields(id, fields, field_value);
        });

        //图片放大
        $(document).on('click', '.image-show', function () {
            var src = $(this).attr('src');
            like.showImg(src, 350);
        });

        // 商品列表
        function getList(type) {
            like.tableLists('#goods-lists', '{:url("goods.goods/lists")}?type=' + type, [
                { type: 'checkbox' }
                , { title: '商品信息', width: 250, templet: '#goods-info' }
                , { field: 'price', title: '价格区间(元)', width: 200, align: 'center' }
                , { field: 'stock', width: 100, title: '总库存', align: 'center' }
                , { field: 'sales_actual', width: 100, title: '总销量', align: 'center' }
                // ,{field: 'is_distribution_desc',width: 100,title: '分销商品', align: 'center'}
                // ,{width: 220,title: '分销比例', align: 'center', templet: '#ratio'}
                , { field: 'audit_remark', width: 180, title: '审核说明', align: 'center' }
                , { field: 'sort', width: 80, title: '排序', align: 'center' }
                , { field: 'create_time', width: 160, title: '发布日期', align: 'center' }
                , { fixed: 'right', title: '操作', width: 250, align: 'center', toolbar: '#goods-operation' }
            ]);
            // if(type == 6) { // 审核未通过,显示审核说明列(注意计时器时间控制，先让动态表格渲染完成，再去移除layui-hide)
            //     setTimeout(function() {
            //         $('.layui-table .layui-hide').removeClass('layui-hide');
            //     }, 1500);
            // }
        }

        // 更新指定字段
        function changeFields(id, fields, value) {
            like.ajax({
                url: '{:url("goods.goods/changeFields")}',
                data: { id: id, field: fields, value: value },
                type: 'post',
                dataType: 'json',
                success: function (res) {
                    if (res.code == 1) {
                        layui.layer.msg(res.msg, {
                            offset: '15px'
                            , icon: 1
                            , time: 1000
                        }, function () {
                            updateTabNumber();
                            table.reload('goods-lists');
                        });
                    }
                }
            })
        }

        // 更新选项卡 统计数据
        function updateTabNumber() {
            like.ajax({
                url: '{:url("goods.goods/totalCount")}',
                data: {},
                type: "GET",
                success: function (res) {
                    if (res.code === 1) {
                        $(".layui-tab-title li[data-type=1]").html("销售中商品(" + res.data.sell + ")");
                        $(".layui-tab-title li[data-type=2]").html("库存预警商品(" + res.data.warn + ")");
                        $(".layui-tab-title li[data-type=3]").html("仓库中商品(" + res.data.warehouse + ")");
                        $(".layui-tab-title li[data-type=4]").html("回收站商品(" + res.data.recycle + ")");
                        $(".layui-tab-title li[data-type=5]").html("待审核商品(" + res.data.audit_stay + ")");
                        $(".layui-tab-title li[data-type=6]").html("审核未通过商品(" + res.data.audit_refuse + ")");
                    }
                }
            });
        }

        // 批量上下架
        function updateStatus(status) {
            var checkStatus = table.checkStatus('goods-lists');
            var checkData = checkStatus.data;
            var ids = [];
            // 取出选中的行ID
            checkData.forEach(function (item) {
                ids.push(parseInt(item['id']));
            });
            if (ids.length <= 0) {
                layui.layer.msg('请选择商品', { time: 1000 });
                return false;
            }
            // 提交数据
            like.ajax({
                url: '{:url("goods.goods/setStatus")}',
                data: { "ids": ids, 'status': status },
                type: "post",
                success: function (res) {
                    if (res.code === 1) {
                        layui.layer.msg(res.msg, { offset: '15px', icon: 1, time: 1000 });
                        updateTabNumber();
                        table.reload('goods-lists', { where: [] });
                    }
                }
            });
        }

    });
</script>