-- 管理员商品编辑权限SQL脚本
-- 为管理员添加商品编辑权限：平台分类、商品详情、销售状态

-- 1. 查找商品管理菜单的ID
-- 假设商品管理菜单已存在，我们需要在其下添加编辑权限

-- 2. 添加商品编辑主菜单（如果不存在）
INSERT INTO `ls_dev_auth` (`pid`, `type`, `system`, `name`, `icon`, `uri`, `sort`, `disable`, `create_time`, `update_time`, `del`) 
VALUES 
(
    (SELECT id FROM (SELECT id FROM `ls_dev_auth` WHERE `name` = '商品管理' AND `type` = 1 LIMIT 1) AS temp), 
    1, 
    0, 
    '商品编辑', 
    '', 
    'goods/goods/edit', 
    50, 
    0, 
    UNIX_TIMESTAMP(), 
    UNIX_TIMESTAMP(), 
    0
);

-- 3. 获取刚插入的商品编辑菜单ID，并添加子权限
SET @goods_edit_menu_id = LAST_INSERT_ID();

-- 4. 添加平台分类编辑权限
INSERT INTO `ls_dev_auth` (`pid`, `type`, `system`, `name`, `icon`, `uri`, `sort`, `disable`, `create_time`, `update_time`, `del`) 
VALUES 
(@goods_edit_menu_id, 2, 0, '编辑商品平台分类', '', 'goods/goods/editCategory', 50, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0);

-- 5. 添加商品详情编辑权限
INSERT INTO `ls_dev_auth` (`pid`, `type`, `system`, `name`, `icon`, `uri`, `sort`, `disable`, `create_time`, `update_time`, `del`) 
VALUES 
(@goods_edit_menu_id, 2, 0, '编辑商品详情', '', 'goods/goods/editContent', 50, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0);

-- 6. 添加销售状态编辑权限
INSERT INTO `ls_dev_auth` (`pid`, `type`, `system`, `name`, `icon`, `uri`, `sort`, `disable`, `create_time`, `update_time`, `del`) 
VALUES 
(@goods_edit_menu_id, 2, 0, '编辑商品销售状态', '', 'goods/goods/editStatus', 50, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0);

-- 注意：
-- 1. 执行此脚本前，请确保 ls_dev_auth 表中已存在"商品管理"菜单
-- 2. 如果商品管理菜单名称不同，请修改第一个查询中的菜单名称
-- 3. 执行后，需要在管理员角色管理中为相应角色分配这些权限
-- 4. type=1 表示菜单，type=2 表示权限
-- 5. system=0 表示非系统权限，可以被删除和修改
